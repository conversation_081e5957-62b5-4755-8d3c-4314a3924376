{"name": "eco-ai-preception-monitor", "version": "2.0", "description": "生物多样性智能监测系统", "author": "", "license": "MIT", "scripts": {"dev": "vite --force", "build": "vite build", "lint-fix": "eslint --fix --ext .js --ext .jsx --ext .vue src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vuemap/vue-amap": "^2.1.1", "@vuemap/vue-amap-loca": "^2.1.2", "alawmulaw": "^6.0.0", "animate.css": "^4.1.1", "axios": "^1.3.5", "clipboard-polyfill": "^4.1.0", "custom-vue-scrollbar": "^0.0.8", "echarts": "^5.3.3", "element-plus": "2.4.3", "fast-glob": "^3.3.2", "js-cookie": "^3.0.1", "json-bigint": "^1.0.0", "lodash": "^4.17.21", "mitt": "^3.0.0", "npm": "^10.8.1", "nprogress": "^0.2.0", "ol": "^10.2.1", "pinia": "^2.0.34", "pinia-plugin-persistedstate": "^3.2.1", "qrcodejs2-fixes": "^0.0.2", "qs": "^6.11.1", "screenfull": "^6.0.2", "sortablejs": "^1.15.0", "video.js": "^8.17.4", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.2.47", "vue-baidu-map-3x": "^1.0.39", "vue-clipboard3": "^2.0.0", "vue-custom-scrollbar": "^1.4.4", "vue-demi": "^0.13.11", "vue-router": "^4.1.6", "vue-video-player": "^6.0.0", "vue3-count-to": "^1.1.2"}, "devDependencies": {"@types/json-bigint": "^1.0.4", "@types/lodash": "^4.17.7", "@types/node": "^18.15.11", "@types/nprogress": "^0.2.0", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "@vitejs/plugin-vue": "^4.1.0", "@vue/compiler-sfc": "^3.2.47", "eslint": "^8.38.0", "eslint-plugin-vue": "^9.10.0", "prettier": "^2.8.7", "sass": "^1.61.0", "typescript": "^5.0.4", "vite": "^4.2.1", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vue-eslint-parser": "^9.1.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "bugs": {"url": "https://gitee.com/lyt-top/vue-next-admin/issues"}, "engines": {"node": ">=16.0.0", "npm": ">= 7.0.0"}, "keywords": ["vue", "vue3", "vuejs/vue-next", "vuejs/vue-next-template", "element-ui", "element-plus", "vue-next-admin", "next-admin"], "repository": {"type": "git", "url": "https://gitee.com/lyt-top/vue-next-admin.git"}}